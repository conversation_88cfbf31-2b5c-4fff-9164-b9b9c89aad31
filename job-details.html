<!doctype html>
<html lang="en">
    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="Job Details - Ed-admin Career Opportunities">
        <meta name="keywords" content="Ed-admin careers, education software jobs, edtech careers, job details">
        <meta name="robots" content="index, follow">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
        
        <!-- Bootstrap Min CSS -->
        <link rel="stylesheet" href="assets/css/bootstrap.min.css">
        <!-- Animate Min CSS -->
        <link rel="stylesheet" href="assets/css/animate.min.css">
        <!-- BoxIcons Min CSS -->
        <link rel="stylesheet" href="assets/css/boxicons.min.css">
        <!-- Owl Carousel Min CSS -->
        <link rel="stylesheet" href="assets/css/owl.carousel.min.css">
        <!-- Odometer Min CSS -->
        <link rel="stylesheet" href="assets/css/odometer.min.css">
        <!-- MeanMenu CSS -->
        <link rel="stylesheet" href="assets/css/meanmenu.css">
        <!-- Magnific Popup Min CSS -->
        <link rel="stylesheet" href="assets/css/magnific-popup.min.css">
        <!-- Style CSS -->
        <link rel="stylesheet" href="assets/css/style.css">
        <!-- Responsive CSS -->
        <link rel="stylesheet" href="assets/css/responsive.css">

        <title>Job Details - Ed-admin Careers</title>
        <link rel="icon" type="image/png" href="assets/img/favicon-Edadmin.ico">

        <!-- Keep original template styling - no custom CSS needed -->
        
        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-B7QM9WG2P4"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-B7QM9WG2P4');
        </script>
    </head>

    <body>
        <span data-menuid="about-Ed-admin" class="d-none"></span>
        <!-- Start PopUps Area -->
            <div data-include="popups/demonow"></div>
            <div data-include="popups/bookdemo"></div>
            <div data-include="popups/downloadnow"></div>
            <div data-include="popups/freedemo"></div>
        <!-- End PopUps Area -->

        <!-- Start Header Area -->
        <div data-include="common/header2"></div>
        <!-- End Header Area -->

        <!-- Original Template Structure - Exactly as designed -->
        <section style="padding: 80px 0; background-color: #ffffff;">
            <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">

                <!-- Overlaying Blocks Section - Exact Original Implementation -->
                <div style="height: 250px; width: 100%; position: relative; display: flex; justify-content: center; align-items: center;">
                    <div style="width: 70%; height: 100px; position: relative;">
                        <div style="width: 100%; height: 100px; background: rgba(63, 81, 181, 0.76); border-radius: 20px; overflow: hidden; position: absolute; top: 0; left: 0;"></div>
                        <div style="width: 100%; height: 100px; background: rgba(63, 81, 181, 0.65); border-radius: 20px; overflow: hidden; position: absolute; top: 0; left: 40px;"></div>
                        <div style="width: 100%; height: 100px; background-color: rgb(63,81,181); border-radius: 20px; overflow: hidden; position: absolute; top: 0; left: 80px;"></div>
                        <div id="job-header-image" style="width: 100%; height: 100px; background-image: url('./assets/img/qa-engineer.png'); background-position: center; background-size: cover; background-repeat: no-repeat; border-radius: 20px; overflow: hidden; position: absolute; top: 0; left: 120px;"></div>
                    </div>
                </div>

                <!-- Job Title Section -->
                <div style="text-align: center; margin: 40px 0;">
                    <h1 id="job-title" style="font-size: 2.5rem; font-weight: 700; color: #333; margin-bottom: 15px;">QA Automation Engineer</h1>
                    <div style="display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;">
                        <span id="job-location" style="color: #666; font-size: 1.1rem;">Remote / San Francisco</span>
                        <span id="job-type" style="color: #666; font-size: 1.1rem;">Full-time</span>
                        <span id="job-department" style="color: #666; font-size: 1.1rem;">Engineering</span>
                    </div>
                </div>

                <!-- 4 Content Sections in 2x2 Grid -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 40px;">

                    <!-- Description Section -->
                    <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">
                        <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 20px; color: #080a3c;">Description</h3>
                        <div id="job-description">
                            <p style="color: #4a6f8a; line-height: 1.6;">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English.</p>
                            <p style="color: #4a6f8a; line-height: 1.6;">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here'.</p>
                        </div>
                    </div>

                    <!-- Benefits Section -->
                    <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">
                        <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 20px; color: #080a3c;">Benefits</h3>
                        <ul id="job-benefits" style="list-style: none; padding: 0;">
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">It is a long established fact that</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">reader will be distracted by the</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">of letters, as opposed to using</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">of a page when looking at its</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">of a page when looking at its</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">readable content of a page</li>
                        </ul>
                    </div>

                    <!-- What You'll Do Section -->
                    <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">
                        <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 20px; color: #080a3c;">What You'll Do</h3>
                        <ul id="job-what-youll-do" style="list-style: none; padding: 0;">
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">it is a long established fact that</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">reader will be distracted by the</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">of letters, as opposed to using</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">of a page when looking at its</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">of letters, as opposed to using</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">readable content of a page</li>
                        </ul>
                    </div>

                    <!-- What You'll Need Section -->
                    <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">
                        <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 20px; color: #080a3c;">What You'll Need</h3>
                        <ul id="job-what-youll-need" style="list-style: none; padding: 0;">
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">it is a long established fact that</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">reader will be distracted by the</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">of letters, as opposed to using</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">of a page when looking at its</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">of letters, as opposed to using</li>
                            <li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">readable content of a page</li>
                        </ul>
                    </div>
                </div>

                <!-- Apply Section - Exact Original Implementation -->
                <div style="width: 70%; display: flex; justify-content: flex-end; margin-top: 30px;">
                    <div onclick="applyForJob()" style="width: 250px; height: 50px; border-radius: 10px; border: 1.5px; border-color: black; border-style: solid; display: flex; justify-content: center; align-items: center; cursor: pointer;">Apply Now</div>
                </div>
            </div>
        </section>

        <!-- Start Footer Area -->
        <div data-include="common/footer"></div>
        <!-- End Footer Area -->

        <div class="go-top"><i class='bx bx-chevron-up'></i></div>

        <script src="/assets/js/cookie.js" type="text/javascript"></script>
        <!-- jQuery Min JS -->
        <script src="assets/js/jquery.min.js"></script>
        <!-- Popper Min JS -->
        <script src="assets/js/popper.min.js"></script>
        <!-- Bootstrap Min JS -->
        <script src="assets/js/bootstrap.min.js"></script>
        <!-- Magnific Popup Min JS -->
        <script src="assets/js/jquery.magnific-popup.min.js"></script>
        <!-- Appear Min JS -->
        <script src="assets/js/jquery.appear.min.js"></script>
        <!-- Odometer Min JS -->
        <script src="assets/js/odometer.min.js"></script>
        <!-- Owl Carousel Min JS -->
        <script src="assets/js/owl.carousel.min.js"></script>
        <!-- MeanMenu JS -->
        <script src="assets/js/jquery.meanmenu.js"></script>
        <!-- WOW Min JS -->
        <script src="assets/js/wow.min.js"></script>
        <!-- Message Conversation JS -->
        <script src="assets/js/conversation.js"></script>
        <!-- AjaxChimp Min JS -->
        <script src="assets/js/jquery.ajaxchimp.min.js"></script>
        <!-- Form Validator Min JS -->
        <script src="assets/js/form-validator.min.js"></script>
        <!-- Contact Form Min JS -->
        <script src="assets/js/contact-form-script.js"></script>
        <!-- Particles Min JS -->
        <script src="assets/js/particles.min.js"></script>
        <script src="assets/js/coustom-particles.js"></script>
        <!-- Main JS -->
        <script src="assets/js/main.js"></script>

        <!-- Job Details JavaScript -->
        <script>
            // API Configuration
            const JOBS_API_URL = 'http://localhost:3001/api/jobs';

            // Get job ID from URL parameters
            function getJobIdFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('id');
            }

            // Load job details
            async function loadJobDetails() {
                const jobId = getJobIdFromUrl();

                if (!jobId) {
                    showError('No job ID specified');
                    return;
                }

                try {
                    const response = await fetch(`${JOBS_API_URL}/${jobId}`);

                    if (!response.ok) {
                        throw new Error('Job not found');
                    }

                    const job = await response.json();
                    displayJobDetails(job);

                } catch (error) {
                    console.error('Error loading job details:', error);
                    showError('Unable to load job details. Please try again later.');
                }
            }

            // Display job details - preserve original template styling
            function displayJobDetails(job) {
                // Update job title and meta
                document.getElementById('job-title').textContent = job.title;
                document.getElementById('job-location').textContent = job.location;
                document.getElementById('job-type').textContent = job.type;
                document.getElementById('job-department').textContent = job.department;

                // Update header image
                if (job.image_url) {
                    document.getElementById('job-header-image').style.backgroundImage = `url('${job.image_url}')`;
                }

                // Update description - preserve paragraph styling
                document.getElementById('job-description').innerHTML = formatDescriptionContent(job.description);

                // Update benefits - preserve list styling
                document.getElementById('job-benefits').innerHTML = formatListContent(job.benefits);

                // Update what you'll do - preserve list styling
                document.getElementById('job-what-youll-do').innerHTML = formatListContent(job.what_youll_do);

                // Update what you'll need - preserve list styling
                document.getElementById('job-what-youll-need').innerHTML = formatListContent(job.what_youll_need);

                // Update page title
                document.title = `${job.title} - Ed-admin Careers`;
            }

            // Format description content - preserve original paragraph styling
            function formatDescriptionContent(text) {
                if (!text) return '<p style="color: #4a6f8a; line-height: 1.6;">No description available.</p>';

                // Split by periods and create paragraphs with original styling
                const sentences = text.split('.').filter(s => s.trim().length > 0);
                const paragraphs = [];

                for (let i = 0; i < sentences.length; i += 2) {
                    const paragraph = sentences.slice(i, i + 2).join('.') + '.';
                    paragraphs.push(`<p style="color: #4a6f8a; line-height: 1.6;">${paragraph}</p>`);
                }

                return paragraphs.join('');
            }

            // Format list content - preserve original list item styling
            function formatListContent(text) {
                if (!text) return '<li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">No information available.</li>';

                // Split by commas or periods and create list items with original styling
                const items = text.split(/[,.]+/).filter(item => item.trim().length > 0);

                return items.map(item =>
                    `<li style="padding: 8px 0; color: #4a6f8a; line-height: 1.6;">${item.trim()}</li>`
                ).join('');
            }

            // Show error message
            function showError(message) {
                const container = document.querySelector('section');
                container.innerHTML = `
                    <div style="text-align: center; padding: 60px 20px;">
                        <i class='bx bx-error' style="font-size: 4rem; color: #dc3545; margin-bottom: 20px;"></i>
                        <h2 style="color: #dc3545; margin-bottom: 15px;">Error</h2>
                        <p style="color: #666; margin-bottom: 30px;">${message}</p>
                        <a href="join-our-team.html" style="display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;">
                            <i class='bx bx-arrow-back'></i>
                            Back to Jobs
                        </a>
                    </div>
                `;
            }

            // Apply for job function
            function applyForJob() {
                const jobId = getJobIdFromUrl();
                // In a real application, this would open an application form or redirect to an application page
                alert(`Application form for job ID: ${jobId} would open here. In a real application, this would redirect to an application form or email.`);
            }

            // Load job details when page loads
            document.addEventListener('DOMContentLoaded', function() {
                loadJobDetails();
            });
        </script>
    </body>
</html>
