<!doctype html>
<html lang="en">
    <head>
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="Job Details - Ed-admin Career Opportunities">
        <meta name="keywords" content="Ed-admin careers, education software jobs, edtech careers, job details">
        <meta name="robots" content="index, follow">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
        
        <!-- Bootstrap Min CSS -->
        <link rel="stylesheet" href="assets/css/bootstrap.min.css">
        <!-- Animate Min CSS -->
        <link rel="stylesheet" href="assets/css/animate.min.css">
        <!-- BoxIcons Min CSS -->
        <link rel="stylesheet" href="assets/css/boxicons.min.css">
        <!-- Owl Carousel Min CSS -->
        <link rel="stylesheet" href="assets/css/owl.carousel.min.css">
        <!-- Odometer Min CSS -->
        <link rel="stylesheet" href="assets/css/odometer.min.css">
        <!-- MeanMenu CSS -->
        <link rel="stylesheet" href="assets/css/meanmenu.css">
        <!-- Magnific Popup Min CSS -->
        <link rel="stylesheet" href="assets/css/magnific-popup.min.css">
        <!-- Style CSS -->
        <link rel="stylesheet" href="assets/css/style.css">
        <!-- Responsive CSS -->
        <link rel="stylesheet" href="assets/css/responsive.css">

        <title>Job Details - Ed-admin Careers</title>
        <link rel="icon" type="image/png" href="assets/img/favicon-Edadmin.ico">

        <!-- Custom CSS for Job Details -->
        <style>
            body {
                font-family: 'Poppins', sans-serif;
                color: #333;
                line-height: 1.6;
            }
            
            .job-details-container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 40px 20px;
            }
            
            .job-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 20px;
                padding: 40px;
                color: white;
                margin-bottom: 40px;
                position: relative;
                overflow: hidden;
            }
            
            .job-header::before {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 200px;
                height: 200px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 50%;
                transform: translate(50%, -50%);
            }
            
            .job-title {
                font-size: 2.5rem;
                font-weight: 700;
                margin-bottom: 10px;
                position: relative;
                z-index: 2;
            }
            
            .job-meta {
                display: flex;
                gap: 20px;
                flex-wrap: wrap;
                position: relative;
                z-index: 2;
            }
            
            .job-meta span {
                background: rgba(255, 255, 255, 0.2);
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 14px;
                font-weight: 500;
            }
            
            .job-content {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 40px;
                margin-bottom: 40px;
            }
            
            .content-section {
                background: white;
                padding: 30px;
                border-radius: 15px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                border-left: 4px solid #FF9300;
            }
            
            .content-section h3 {
                font-size: 1.5rem;
                font-weight: 600;
                margin-bottom: 20px;
                color: #080a3c;
            }
            
            .content-section ul {
                list-style: none;
                padding: 0;
            }
            
            .content-section li {
                padding: 8px 0;
                padding-left: 20px;
                position: relative;
                color: #4a6f8a;
                line-height: 1.6;
            }
            
            .content-section li::before {
                content: '•';
                color: #FF9300;
                font-weight: bold;
                position: absolute;
                left: 0;
            }
            
            .apply-section {
                text-align: center;
                background: #f8f9fa;
                padding: 40px;
                border-radius: 15px;
                margin-top: 40px;
            }
            
            .apply-btn {
                display: inline-flex;
                justify-content: center;
                align-items: center;
                gap: 10px;
                border: 1.17px solid #000000;
                border-radius: 21.66px;
                width: 150px;
                height: 50px;
                padding: 12px 30px;
                background: white;
                color: black;
                text-decoration: none;
                font-weight: 500;
                transition: all 0.3s ease;
                cursor: pointer;
            }
            
            .apply-btn:hover {
                background: #000000;
                color: white;
                text-decoration: none;
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            }
            
            .back-link {
                display: inline-flex;
                align-items: center;
                gap: 8px;
                color: #006EB3;
                text-decoration: none;
                font-weight: 500;
                margin-bottom: 30px;
                transition: color 0.3s ease;
            }
            
            .back-link:hover {
                color: #FF9300;
                text-decoration: none;
            }
            
            @media (max-width: 768px) {
                .job-content {
                    grid-template-columns: 1fr;
                    gap: 20px;
                }
                
                .job-title {
                    font-size: 2rem;
                }
                
                .job-header {
                    padding: 30px 20px;
                }
                
                .content-section {
                    padding: 20px;
                }
            }
        </style>
        
        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-B7QM9WG2P4"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-B7QM9WG2P4');
        </script>
    </head>

    <body>
        <span data-menuid="about-Ed-admin" class="d-none"></span>
        <!-- Start PopUps Area -->
            <div data-include="popups/demonow"></div>
            <div data-include="popups/bookdemo"></div>
            <div data-include="popups/downloadnow"></div>
            <div data-include="popups/freedemo"></div>
        <!-- End PopUps Area -->

        <!-- Start Header Area -->
        <div data-include="common/header2"></div>
        <!-- End Header Area -->

        <!-- Job Details Content -->
        <div class="job-details-container">
            <!-- Back Link -->
            <a href="join-our-team.html" class="back-link">
                <i class='bx bx-arrow-back'></i> Back to Jobs
            </a>

            <!-- Job Header with Overlaying Blocks -->
            <div style="height: 250px; width: 100%; position: relative; display: flex; justify-content: center; align-items: center;">
                <div style="width: 70%; height: 100px; position: relative;">
                    <div style="width: 100%; height: 100px; background: rgba(63, 81, 181, 0.76); border-radius: 20px; overflow: hidden; position: absolute; top: 0; left: 0;"></div>
                    <div style="width: 100%; height: 100px; background: rgba(63, 81, 181, 0.65); border-radius: 20px; overflow: hidden; position: absolute; top: 0; left: 40px;"></div>
                    <div style="width: 100%; height: 100px; background-color: rgb(63,81,181); border-radius: 20px; overflow: hidden; position: absolute; top: 0; left: 80px;"></div>
                    <div id="job-header-image" style="width: 100%; height: 100px; background-image: url('./assets/img/qa-engineer.png'); background-position: center; background-size: cover; background-repeat: no-repeat; border-radius: 20px; overflow: hidden; position: absolute; top: 0; left: 120px;"></div>
                </div>
            </div>

            <!-- Job Title and Meta -->
            <div style="text-align: center; margin: 40px 0;">
                <h1 id="job-title" class="job-title">QA Automation Engineer</h1>
                <div id="job-meta" class="job-meta" style="justify-content: center;">
                    <span id="job-location">Remote / San Francisco</span>
                    <span id="job-type">Full-time</span>
                    <span id="job-department">Engineering</span>
                </div>
            </div>

            <!-- Job Content Tabs -->
            <div class="job-content">
                <!-- Description Section -->
                <div class="content-section">
                    <h3>Description</h3>
                    <div id="job-description">
                        <p>Loading job description...</p>
                    </div>
                </div>

                <!-- Benefits Section -->
                <div class="content-section">
                    <h3>Benefits</h3>
                    <ul id="job-benefits">
                        <li>Loading benefits...</li>
                    </ul>
                </div>

                <!-- What You'll Do Section -->
                <div class="content-section">
                    <h3>What You'll Do</h3>
                    <ul id="job-what-youll-do">
                        <li>Loading responsibilities...</li>
                    </ul>
                </div>

                <!-- What You'll Need Section -->
                <div class="content-section">
                    <h3>What You'll Need</h3>
                    <ul id="job-what-youll-need">
                        <li>Loading requirements...</li>
                    </ul>
                </div>
            </div>

            <!-- Apply Section -->
            <div class="apply-section">
                <h3>Ready to Join Our Team?</h3>
                <p>Take the next step in your career and become part of our innovative team.</p>
                <a href="#" class="apply-btn" onclick="applyForJob()">
                    <span>Apply Now</span>
                    <img src="assets/img/arrow-right.svg" alt="arrow" style="width: 16px; height: 16px;"/>
                </a>
            </div>
        </div>

        <!-- Start Footer Area -->
        <div data-include="common/footer"></div>
        <!-- End Footer Area -->

        <div class="go-top"><i class='bx bx-chevron-up'></i></div>

        <script src="/assets/js/cookie.js" type="text/javascript"></script>
        <!-- jQuery Min JS -->
        <script src="assets/js/jquery.min.js"></script>
        <!-- Popper Min JS -->
        <script src="assets/js/popper.min.js"></script>
        <!-- Bootstrap Min JS -->
        <script src="assets/js/bootstrap.min.js"></script>
        <!-- Magnific Popup Min JS -->
        <script src="assets/js/jquery.magnific-popup.min.js"></script>
        <!-- Appear Min JS -->
        <script src="assets/js/jquery.appear.min.js"></script>
        <!-- Odometer Min JS -->
        <script src="assets/js/odometer.min.js"></script>
        <!-- Owl Carousel Min JS -->
        <script src="assets/js/owl.carousel.min.js"></script>
        <!-- MeanMenu JS -->
        <script src="assets/js/jquery.meanmenu.js"></script>
        <!-- WOW Min JS -->
        <script src="assets/js/wow.min.js"></script>
        <!-- Message Conversation JS -->
        <script src="assets/js/conversation.js"></script>
        <!-- AjaxChimp Min JS -->
        <script src="assets/js/jquery.ajaxchimp.min.js"></script>
        <!-- Form Validator Min JS -->
        <script src="assets/js/form-validator.min.js"></script>
        <!-- Contact Form Min JS -->
        <script src="assets/js/contact-form-script.js"></script>
        <!-- Particles Min JS -->
        <script src="assets/js/particles.min.js"></script>
        <script src="assets/js/coustom-particles.js"></script>
        <!-- Main JS -->
        <script src="assets/js/main.js"></script>

        <!-- Job Details JavaScript -->
        <script>
            // API Configuration
            const JOBS_API_URL = 'http://localhost:3001/api/jobs';

            // Get job ID from URL parameters
            function getJobIdFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('id');
            }

            // Load job details
            async function loadJobDetails() {
                const jobId = getJobIdFromUrl();

                if (!jobId) {
                    showError('No job ID specified');
                    return;
                }

                try {
                    const response = await fetch(`${JOBS_API_URL}/${jobId}`);

                    if (!response.ok) {
                        throw new Error('Job not found');
                    }

                    const job = await response.json();
                    displayJobDetails(job);

                } catch (error) {
                    console.error('Error loading job details:', error);
                    showError('Unable to load job details. Please try again later.');
                }
            }

            // Display job details
            function displayJobDetails(job) {
                // Update job title and meta
                document.getElementById('job-title').textContent = job.title;
                document.getElementById('job-location').textContent = job.location;
                document.getElementById('job-type').textContent = job.type;
                document.getElementById('job-department').textContent = job.department;

                // Update header image
                if (job.image_url) {
                    document.getElementById('job-header-image').style.backgroundImage = `url('${job.image_url}')`;
                }

                // Update description
                document.getElementById('job-description').innerHTML = formatTextContent(job.description);

                // Update benefits
                document.getElementById('job-benefits').innerHTML = formatListContent(job.benefits);

                // Update what you'll do
                document.getElementById('job-what-youll-do').innerHTML = formatListContent(job.what_youll_do);

                // Update what you'll need
                document.getElementById('job-what-youll-need').innerHTML = formatListContent(job.what_youll_need);

                // Update page title
                document.title = `${job.title} - Ed-admin Careers`;
            }

            // Format text content (for description)
            function formatTextContent(text) {
                if (!text) return '<p>No description available.</p>';

                // Split by periods and create paragraphs
                const sentences = text.split('.').filter(s => s.trim().length > 0);
                const paragraphs = [];

                for (let i = 0; i < sentences.length; i += 2) {
                    const paragraph = sentences.slice(i, i + 2).join('.') + '.';
                    paragraphs.push(`<p>${paragraph}</p>`);
                }

                return paragraphs.join('');
            }

            // Format list content (for benefits, what you'll do, what you'll need)
            function formatListContent(text) {
                if (!text) return '<li>No information available.</li>';

                // Split by commas or periods and create list items
                const items = text.split(/[,.]+/).filter(item => item.trim().length > 0);

                return items.map(item => `<li>${item.trim()}</li>`).join('');
            }

            // Show error message
            function showError(message) {
                const container = document.querySelector('.job-details-container');
                container.innerHTML = `
                    <div style="text-align: center; padding: 60px 20px;">
                        <i class='bx bx-error' style="font-size: 4rem; color: #dc3545; margin-bottom: 20px;"></i>
                        <h2 style="color: #dc3545; margin-bottom: 15px;">Error</h2>
                        <p style="color: #666; margin-bottom: 30px;">${message}</p>
                        <a href="join-our-team.html" class="apply-btn">
                            <i class='bx bx-arrow-back'></i>
                            <span>Back to Jobs</span>
                        </a>
                    </div>
                `;
            }

            // Apply for job function
            function applyForJob() {
                const jobId = getJobIdFromUrl();
                // In a real application, this would open an application form or redirect to an application page
                alert(`Application form for job ID: ${jobId} would open here. In a real application, this would redirect to an application form or email.`);
            }

            // Load job details when page loads
            document.addEventListener('DOMContentLoaded', function() {
                loadJobDetails();
            });
        </script>
    </body>
</html>
